import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export interface SubscriptionStatus {
    is_active: boolean;
    expires_at: string | null;
    loading: boolean;
    error: string | null;
    checked: boolean; // Track if we've checked the status at least once
}

const STORAGE_KEY = 'subscription_status';

// Load initial state from localStorage if available
function getInitialState(): SubscriptionStatus {
    if (browser) {
        try {
            const stored = localStorage.getItem(STORAGE_KEY);
            if (stored) {
                const parsed = JSON.parse(stored);
                // Only restore non-loading states
                return {
                    ...parsed,
                    loading: false, // Always reset loading state
                    error: null     // Reset errors on page load
                };
            }
        } catch (error) {
            console.warn('Error loading subscription status from localStorage:', error);
        }
    }
    
    return {
        is_active: false,
        expires_at: null,
        loading: false,
        error: null,
        checked: false
    };
}

function createSubscriptionStatusStore() {
    const { subscribe, set, update } = writable<SubscriptionStatus>(getInitialState());

    // Save to localStorage whenever the store updates
    const saveToStorage = (state: SubscriptionStatus) => {
        if (browser) {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
            } catch (error) {
                console.warn('Error saving subscription status to localStorage:', error);
            }
        }
    };

    return {
        subscribe,
        setLoading: (loading: boolean) => {
            update(state => {
                const newState = { ...state, loading };
                saveToStorage(newState);
                return newState;
            });
        },
        setStatus: (is_active: boolean, expires_at: string) => {
            const newState = {
                is_active,
                expires_at,
                loading: false,
                error: null,
                checked: true
            };
            update(state => {
                const updatedState = { ...state, ...newState };
                saveToStorage(updatedState);
                return updatedState;
            });
            return newState;
        },
        setError: (error: string) => {
            update(state => {
                const newState = { 
                    ...state, 
                    error, 
                    loading: false, 
                    checked: true 
                };
                saveToStorage(newState);
                return newState;
            });
        },
        reset: () => {
            const resetState = {
                is_active: false,
                expires_at: null,
                loading: false,
                error: null,
                checked: false
            };
            set(resetState);
            if (browser) {
                localStorage.removeItem(STORAGE_KEY);
            }
        }
    };
}

// Utility function to read from localStorage
export function getSubscriptionFromStorage(): SubscriptionStatus | null {
    if (!browser) return null;
    try {
        const stored = localStorage.getItem(STORAGE_KEY);
        return stored ? JSON.parse(stored) : null;
    } catch (error) {
        console.warn('Error reading subscription status from localStorage:', error);
        return null;
    }
}

export const subscriptionStatus = createSubscriptionStatusStore();
