<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { PageData } from './$types';

	import CurrentSubscriptionCard from '$lib/components/settings/subscription/CurrentSubscriptionCard.svelte';
	import UsageStatistics from '$lib/components/settings/subscription/UsageStatistics.svelte';

	export let data: PageData;
	$: ({ subscriptionData, error } = data);
</script>

<svelte:head>
	<title>{t('subscription')}</title>
</svelte:head>

<div class="min-h-screen rounded-lg bg-white">
	<div id="subscription-container" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900">
				{t('subscription_title')}
			</h1>
			<p class="mt-2 text-gray-600">
				{t('subscription_description')}
			</p>
		</div>

		{#if error}
			<div class="mb-6 rounded-lg bg-red-50 p-4 border border-red-200">
				<p class="text-red-800">{error}</p>
			</div>
		{:else if subscriptionData}
			<!-- Subscription Content -->
			<div id="subscription-content" class="space-y-6">
				<CurrentSubscriptionCard
					subscription={subscriptionData.currentSubscription}
				/>
				<UsageStatistics
					quotaData={subscriptionData.quotaData}
				/>
			</div>
		{:else}
			<div class="text-center py-12">
				<p class="text-gray-500">Loading subscription data...</p>
			</div>
		{/if}
	</div>
</div>
